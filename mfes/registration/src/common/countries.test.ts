import { SupportedLocale } from '@bp/pulse-shared-types/lib/enums/SupportedLocale';

import { getCountryDetails, getInitialCountryByLocale } from './countries';
import { AppCountry, LanguageCodeISO } from './enums';
import { CustomiseCountry } from './interfaces';

const mockBrandCountries = [
  {
    type: AppCountry.NETHERLANDS,
    icon: '🇳🇱',
  },
  {
    type: AppCountry.UK,
    icon: '🇬🇧',
  },
];

const mockNLLocale = LanguageCodeISO.DUTCH;
const localisedNLCountryName = 'Nederland';

const mockUKLocale = LanguageCodeISO.ENGLISH;
const localisedUKCountryName = 'United Kingdom';

const mockNLCountryDetails: CustomiseCountry = {
  name: localisedNLCountryName,
  displayName: `${mockBrandCountries[0].icon} ${localisedNLCountryName}`,
  icon: mockBrandCountries[0].icon,
  appCountry: AppCountry.NETHERLANDS,
};

const mockUKCountryDetails = (
  localisedName = localisedUKCountryName,
): CustomiseCountry => ({
  name: localisedName,
  displayName: `${mockBrandCountries[1].icon} ${localisedName}`,
  icon: mockBrandCountries[1].icon,
  appCountry: AppCountry.UK,
});

describe('getCountryDetails utility', () => {
  it('Should return the correct country details for NL brand country and short form locale', () => {
    const countryDetails = getCountryDetails(
      mockBrandCountries[0],
      mockNLLocale,
    );
    expect(countryDetails).toEqual(mockNLCountryDetails);
  });
  it('Should return the correct country details for UK brand country and short form locale', () => {
    const countryDetails = getCountryDetails(
      mockBrandCountries[1],
      mockUKLocale,
    );
    expect(countryDetails).toEqual(mockUKCountryDetails());
  });
});

describe('getInitialCountryByLocale utility', () => {
  it('Should return the details for the initial selected country based on the long form locale', () => {
    const initialCountryDetails = getInitialCountryByLocale(
      mockBrandCountries,
      SupportedLocale.NL_NL,
      AppCountry.UK,
    );
    expect(initialCountryDetails).toEqual(mockNLCountryDetails);
  });
  it('Should return UK country details as the initial selected country if the provided locale is not supported', () => {
    const initialCountryDetails = getInitialCountryByLocale(
      mockBrandCountries,
      SupportedLocale.FR_FR,
      AppCountry.UK,
    );
    expect(initialCountryDetails).toEqual(mockUKCountryDetails('Royaume-Uni'));
  });
});
