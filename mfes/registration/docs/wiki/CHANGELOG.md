# [@bp/registration-mfe-v4.3.0](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/compare/registration/v4.2.0...registration/v4.3.0) (2025-05-01)

# [@bp/registration-mfe-v4.2.0](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/compare/registration/v4.1.2...registration/v4.2.0) (2025-04-28)

# [@bp/registration-mfe-v4.1.2](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/compare/registration/v4.1.1...registration/v4.1.2) (2025-02-11)

### Bug Fixes

- Types in monorepo ([3c4c7cc](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/3c4c7ccae5e8b8d1c2c58ce53fe7102eb6f2e249))

# [@bp/registration-mfe-v4.1.1](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/compare/registration/v4.1.0...registration/v4.1.1) (2025-01-14)

### Bug Fixes

- consent tests ([fcf1dc5](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/fcf1dc5c4b560a33f9ed1119d0153774cc42a264))
- **translations:** new translations messages.json from Crowdin ([e790670](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/e79067095f9f5b1e8ef9a01415830d28b0e7c2a5))
- **translations:** new translations messages.json from Crowdin ([18e069e](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/18e069e4dfba91e6f5fea116900566dd709c6f13))
- **translations:** new translations messages.json from Crowdin ([0389419](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/038941988affa01428de442af7019498d3eeaeba))
- **translations:** new translations messages.json from Crowdin ([2de63c9](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/2de63c981bd126d1eb455911347d6181a04ef8ef))
- **translations:** new translations messages.json from Crowdin ([93e7bd5](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/93e7bd54714d7168d5daf24c7064fb4d126b1362))
- **translations:** new translations messages.json from Crowdin ([ddbe642](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/ddbe642d28a552602813a3606411316c3921c351))
- **translations:** new translations messages.json from Crowdin ([daa7a07](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/daa7a07ed2674060d21b0cda1fca1d09461b62fd))
- **translations:** new translations messages.json from Crowdin ([70c37d5](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/70c37d5cd2bd94aac482e337649ba0c1827ebcb0))
- **translations:** new translations messages.json from Crowdin ([c73631d](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/c73631d86ec362aa4d84d9b4c24395d47814140e))
- **translations:** new translations messages.json from Crowdin ([ae050b5](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/ae050b519406a2e0bd0e02d52190c6a90a9e089e))

# [@bp/registration-mfe-v4.1.0](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/compare/registration/v4.0.2...registration/v4.1.0) (2024-12-05)

# [@bp/registration-mfe-v4.0.2](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/compare/registration/v4.0.1...registration/v4.0.2) (2024-11-26)

### Bug Fixes

- **translations:** new translations messages.json from Crowdin ([8695649](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/8695649c236772e02a32e6784cba6cccc370addd))
- **translations:** new translations messages.json from Crowdin ([e8506e6](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/e8506e6a0c17e8f10f5a336f04bbd0a07aed4a97))

# [@bp/registration-mfe-v4.0.1](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/compare/registration/v4.0.0...registration/v4.0.1) (2024-11-06)

# [@bp/registration-mfe-v4.0.0](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/compare/registration/v3.73.1...registration/v4.0.0) (2024-11-05)

### Features

- **registration:** Modify semantic release config ([2f73475](https://dev.azure.com/bp-digital/bp_pulse/_git/bp-pulse-mobile/commit/2f734755a56fe6a5cd2a1e8472f8e031d37acdc8))

### BREAKING CHANGES

- **registration:** Modify package root

##[3.73.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.73.0&targetVersion=GTv3.73.1&_a=commits) (2024-10-17)

### Bug Fixes

- new translations messages.json from Crowdin ([f5f31b0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/f5f31b0ce46126aa9bb6fb364d77e56bc99927a3)) #7481050

#[3.73.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.72.0&targetVersion=GTv3.73.0&_a=commits) (2024-10-02)

### Features

- us consent types ([56f2574](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/56f25740621a95fb279e0456fc7b8e11a8e95c3d)) #7313821

#[3.72.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.71.3&targetVersion=GTv3.72.0&_a=commits) (2024-08-29)

### Features

- manual version increased ([c9d41d4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/c9d41d48810afe098ce71a0e93b7c7a5c690430f)) #6209656

##[3.71.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.71.2&targetVersion=GTv3.71.3&_a=commits) (2024-07-17)

### Bug Fixes

- **customise:** hide name inputs when nickName exists ([0e3ae5d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/0e3ae5d0c99ba2e61ec278c11a7ddfdfcb0f6fe6)) #7003880

##[3.71.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.71.1&targetVersion=GTv3.71.2&_a=commits) (2024-07-16)

### Bug Fixes

- **customise:** fix crash when nickName is null ([06f4de1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/06f4de158d94c89ea88438cd0c4ed19f59f8805e)) #7003880

##[3.71.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.71.0&targetVersion=GTv3.71.1&_a=commits) (2024-07-16)

### Bug Fixes

- **customise:** parse nickName from Apple SSO into first and last names ([74fd608](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/74fd608f12f099100e421feeb5028c0d88ccd25d)) #7003880
  #7003880

#[3.71.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.70.1&targetVersion=GTv3.71.0&_a=commits) (2024-06-20)

### Features

- manual version update ([403906c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/403906cf9724118b434af6c4b345bf51bc90dd56)) #6884536

##[3.70.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.70.0&targetVersion=GTv3.70.1&_a=commits) (2024-04-12)

### Bug Fixes

- update email marketing consent value on multi consent selection ([72f77c7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/72f77c79284b585727f22e43a0410de5523cddfe)) #6530722

#[3.70.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.69.5&targetVersion=GTv3.70.0&_a=commits) (2024-04-10)

### Features

- **analytics:** remove CIP analytics ([afa8b73](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/afa8b730aba41562f37e7edcc4ada5e17c33f083)) #6475228

##[3.69.5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.69.4&targetVersion=GTv3.69.5&_a=commits) (2024-03-12)

### Bug Fixes

- **analytics:** fix analytics for cip registration and error ([86abb32](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/86abb325b7b47e8c0f6eaf83e6c34eec62da439b)) #6088829

##[3.69.4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.69.3&targetVersion=GTv3.69.4&_a=commits) (2024-03-06)

### Bug Fixes

- new translations %original_file_name% from Crowdin ([ac66a9c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/ac66a9c9b7473078b46c8cb46451187622df85dd)) #6166382

##[3.69.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.69.2&targetVersion=GTv3.69.3&_a=commits) (2024-03-06)

### Bug Fixes

- update ui components version ([dead530](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/dead5306c370e42a8542849b850478a52e8177f8)) #6339791

##[3.69.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.69.1&targetVersion=GTv3.69.2&_a=commits) (2024-03-04)

### Bug Fixes

- remove country props ([bda509f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/bda509f26947a15fd342950b624262749a282996)) #6328541

##[3.69.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.69.0&targetVersion=GTv3.69.1&_a=commits) (2024-03-04)

### Bug Fixes

- remove country code map using gb instead of uk ([2bf5026](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/2bf5026a6ee024c6592e755ef19bb48f5de9e766)) #6328541

#[3.69.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.68.1&targetVersion=GTv3.69.0&_a=commits) (2024-02-23)

### Features

- add translation placeholder tests and restore missing pricing list plac... ([63bd16b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/63bd16baa1abcf423ecae60374c2c5d6032766ca)) #6270966

##[3.68.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.68.0&targetVersion=GTv3.68.1&_a=commits) (2024-02-21)

### Bug Fixes

- new translations %original_file_name% from Crowdin ([5132399](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/5132399daf64030ee6e6ad8d051dca69ee14254a))

#[3.68.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.67.2&targetVersion=GTv3.68.0&_a=commits) (2024-02-21)

### Features

- hide country selector and set aral defaults ([36073f5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/36073f5eb7b7a071f623e16e92f4778061ec3a43)) #6219498
  #6243958

##[3.67.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.67.1&targetVersion=GTv3.67.2&_a=commits) (2024-02-20)

### Bug Fixes

- new translations %original_file_name% from Crowdin ([1911ab3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/1911ab3cd9b5112b58a642202ce8c8bf3f7ec0d0)) #6243010

##[3.67.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.67.0&targetVersion=GTv3.67.1&_a=commits) (2024-02-20)

### Bug Fixes

- new translations %original_file_name% from Crowdin ([4bd9ab6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/4bd9ab699fa78e7c0055976667392742eac4f3d9)) #6243010

#[3.67.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.66.0&targetVersion=GTv3.67.0&_a=commits) (2024-01-23)

### Features

- updated brand translations ([7002bcc](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/7002bccaac57361f7f59842a547f3117230be582)) #5856677

#[3.66.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.65.0&targetVersion=GTv3.66.0&_a=commits) (2024-01-19)

### Features

- update customise get country details and bottom margin ([b335e08](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/b335e08547621df3f660607fe20d33cb11e8b2ec)) #6101808

#[3.65.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.64.0&targetVersion=GTv3.65.0&_a=commits) (2024-01-16)

### Features

- update translation schemas and remove unused translations ([8b2c352](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/8b2c352972ab810554fdd41aeedc759a66e628bb)) #6074542

#[3.64.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.63.0&targetVersion=GTv3.64.0&_a=commits) (2024-01-15)

### Features

- add dynamic text component ([0f36c71](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/0f36c7155877d0264a446e243def936728c4bc0f)) #6058300

#[3.63.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.62.0&targetVersion=GTv3.63.0&_a=commits) (2024-01-11)

### Features

- sf idp code clean up ([84f84c5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/84f84c50dbd319f1c877345dfd9978fa1c536ec5)) #6037649
  #6037678

#[3.62.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.61.1&targetVersion=GTv3.62.0&_a=commits) (2024-01-05)

### Features

- show pricing for German user ([d461cd0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/d461cd0b9f301ee03a7171e20aab471ca18311f6)) #5749613

##[3.61.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.61.0&targetVersion=GTv3.61.1&_a=commits) (2024-01-04)

### Bug Fixes

- new translations %original_file_name% from Crowdin ([6274b1f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/6274b1ffad1a3683ba7c6dab912f02c601cb9d98)) #5983404

#[3.61.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.60.1&targetVersion=GTv3.61.0&_a=commits) (2023-12-28)

### Features

- adding in external links without ui changes ([ca4c74f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/ca4c74fddcfa4483d8506af544cff87d38956554)) #5749613

##[3.60.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.60.0&targetVersion=GTv3.60.1&_a=commits) (2023-12-21)

### Bug Fixes

- new translations %original_file_name% from Crowdin ([f2400eb](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/f2400eb8c279084de23b505bf137abca942cc575)) #5939794

#[3.60.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.59.1&targetVersion=GTv3.60.0&_a=commits) (2023-12-21)

### Features

- add new marketing consents flag ([a7988e4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/a7988e45576b81d696b311e26effb496787d2f90)) #5987333
  #5987333

##[3.59.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.59.0&targetVersion=GTv3.59.1&_a=commits) (2023-12-14)

### Bug Fixes

- adding support for ES consent types ([cf4e705](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/cf4e705dfc034702407b57bc595b1d7a88f7b24e)) #5874577

#[3.59.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.58.2&targetVersion=GTv3.59.0&_a=commits) (2023-12-12)

### Features

- upgrade ui components ([c308d9b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/c308d9be9c720b9cc88919e3bb3469fe924f5975)) #5946066

##[3.58.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.58.1&targetVersion=GTv3.58.2&_a=commits) (2023-12-11)

### Bug Fixes

- new translations %original_file_name% from Crowdin ([236fa54](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/236fa5418a8009207808f613f455efad48d30e80)) #5940392

##[3.58.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.58.0&targetVersion=GTv3.58.1&_a=commits) (2023-12-08)

### Bug Fixes

- new translations %original_file_name% from Crowdin ([a638f00](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/a638f000ec822823bee50db7e0a087bd426d2bdf)) #5933764

#[3.58.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.57.2&targetVersion=GTv3.58.0&_a=commits) (2023-12-05)

### Features

- updating marketing consents and drop downs ([cb24912](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/cb24912e0b3fb6e4fa15e3ba852a3b65b38bf06d)) #5672500

##[3.57.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.57.1&targetVersion=GTv3.57.2&_a=commits) (2023-12-05)

### Bug Fixes

- update spanish links for t&c and privacy policy ([ace096d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/ace096d2bb849b9fdf0612c64cffdbfb202b24b5)) #5910090
  #5910090

##[3.57.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.57.0&targetVersion=GTv3.57.1&_a=commits) (2023-12-04)

### Bug Fixes

- update onboarding screen ([0b6a759](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/0b6a7596d2bbbc134baabff98d356e14a2a728be)) #5910575
  #5910575

#[3.57.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.56.0&targetVersion=GTv3.57.0&_a=commits) (2023-12-04)

### Features

- integrate brand countries ([0b89c44](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/0b89c4412ae3862fb70c43df498873c097e8ab03)) #5821855

#[3.56.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.55.0&targetVersion=GTv3.56.0&_a=commits) (2023-12-01)

### Features

- update to consents with NL ([14ac492](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/14ac492017b5431537edf20d22b491faac362320)) #5874577

#[3.55.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.54.3&targetVersion=GTv3.55.0&_a=commits) (2023-11-30)

### Features

- update component library ([f3e71ee](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/f3e71ee4bba0122728614597125c0d545228ff29)) #5899060

##[3.54.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.54.2&targetVersion=GTv3.54.3&_a=commits) (2023-11-30)

### Bug Fixes

- update links ([93f36fe](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/93f36fe3263e6ea39b4f9b80131f81cc948255c6)) #5898267

##[3.54.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.54.1&targetVersion=GTv3.54.2&_a=commits) (2023-11-29)

### Bug Fixes

- updating ui-components ([cacd797](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/cacd797be5261ef42e5bfd03919da03cd08d6b0e)) #5844300

##[3.54.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.54.0&targetVersion=GTv3.54.1&_a=commits) (2023-11-27)

### Bug Fixes

- new translations %original_file_name% from Crowdin ([39497e3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/39497e39b8e8d7db0df3cb4d34d57507b810937a)) #5857937

#[3.54.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.53.2&targetVersion=GTv3.54.0&_a=commits) (2023-11-27)

### Features

- add function comment ([84853ef](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/84853efb5c3bb92492b4ca949fb184d54c4dc112)) #5853008

##[3.53.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.53.1&targetVersion=GTv3.53.2&_a=commits) (2023-11-21)

### Bug Fixes

- increase country drowpdown stacking order ([4f980a2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/4f980a2d1715f83449a922ec44b1899603d8f3c2)) #5844300

##[3.53.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.53.0&targetVersion=GTv3.53.1&_a=commits) (2023-11-18)

### Bug Fixes

- include spain to the onboarding country options on settings provider ([6fb61e0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/6fb61e0667464cbc75078feb88bd8ced17cf5e94)) #5836585

#[3.53.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.52.0&targetVersion=GTv3.53.0&_a=commits) (2023-11-15)

### Features

- **customize:** split first name and last name from social registration CIP bug ([1fccf21](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/1fccf21e697642e6a141a367ee85936bd9b6dd3f)) #5822254

#[3.52.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.51.0&targetVersion=GTv3.52.0&_a=commits) (2023-11-15)

### Features

- creating this branch to avoid auto push of translation tooling branch ([fd93617](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/fd936171fd546420f6ff25f11047321577398d61)) #5733416

#[3.51.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.50.1&targetVersion=GTv3.51.0&_a=commits) (2023-11-13)

### Features

- bump auth sdk version ([aab78d2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/aab78d2dcf40f6df058f83773e05cc95c15f7a91)) #5812410

##[3.50.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.50.0&targetVersion=GTv3.50.1&_a=commits) (2023-11-09)

### Bug Fixes

- **country prop:** pass country prop through from customise screen ([6ba0218](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/6ba0218fd98755a4fc05aac30bbdb11f557d8b2e)) #5796243

#[3.50.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.49.0&targetVersion=GTv3.50.0&_a=commits) (2023-11-08)

### Features

- integrate shared types package and refactor locale type ([2833414](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/28334141eb8891739df1751996023fc9175f448e)) #5733628

#[3.49.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.48.1&targetVersion=GTv3.49.0&_a=commits) (2023-11-01)

### Features

- add enable country selection feature flag ([6a913ff](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/6a913ff5d42921e3abb0c8fb0a701127a8d2fde3)) #5762612

##[3.48.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.48.0&targetVersion=GTv3.48.1&_a=commits) (2023-11-01)

### Bug Fixes

- **country selection:** call onCountryChange callback when country value changes ([5dafb87](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/5dafb87fe9c6aba1e9a91d235653427fcd9516c7)) #5759600

#[3.48.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.47.0&targetVersion=GTv3.48.0&_a=commits) (2023-10-24)

### Features

- update to analytics ([bc25f71](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/bc25f71174117a8bb51c656703f4863103f458e5)) #5661239

#[3.47.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.46.7&targetVersion=GTv3.47.0&_a=commits) (2023-10-18)

### Features

- updating logic to external links ([b5294bf](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/b5294bf0d0169b2be2668139dd453bda7faac809)) #5652547
  #5685391

##[3.46.7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.46.6&targetVersion=GTv3.46.7&_a=commits) (2023-10-17)

### Bug Fixes

- invalidate first and last name inputs when containing emojis in value ([4b385bb](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/4b385bb5a4d647b5134c3e84fd3ba76cd9368787)) #5680617
  #5680617

##[3.46.6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.46.5&targetVersion=GTv3.46.6&_a=commits) (2023-10-17)

### Bug Fixes

- solving small viewport issue by updating views to be scroll views ([822af56](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/822af5656fc410678d55d36f9576de1651733cf0)) #5619448

##[3.46.5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.46.4&targetVersion=GTv3.46.5&_a=commits) (2023-10-16)

### Bug Fixes

- update alert modal with new text and one action button ([17a5d2a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/17a5d2a4e7134048fa7de3284ad24610cbe332c3)) #5645964
  #5595717

##[3.46.4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.46.3&targetVersion=GTv3.46.4&_a=commits) (2023-10-13)

### Bug Fixes

- endless redirects ([1ec11b9](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/1ec11b9948aeb181abef587e0b7989d29a8db38f)) #5680428

##[3.46.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.46.2&targetVersion=GTv3.46.3&_a=commits) (2023-10-12)

### Bug Fixes

- new translations %original_file_name% from Crowdin ([251411a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/251411af4e1341cd2a0d80b780bf67e2da918f8f)) #5673965

##[3.46.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.46.1&targetVersion=GTv3.46.2&_a=commits) (2023-10-11)

### Bug Fixes

- test on email verification and minor code refactor on sandbox and route... ([d552bc7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/d552bc7cc9d2c12e95b7e250ca345b6b6253705e)) #5673775
  #5673775

##[3.46.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.46.0&targetVersion=GTv3.46.1&_a=commits) (2023-10-06)

### Bug Fixes

- new translations %original_file_name% from Crowdin ([d0b3131](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/d0b3131a4f6ffdd6c66513c6126b0eadd0cc4129)) #5646567

#[3.46.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.45.1&targetVersion=GTv3.46.0&_a=commits) (2023-10-05)

### Features

- bump auth sdk version ([221ebdd](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/221ebdd07b30be4808f4bd1fad4b64617e3af9cf)) #5645024

##[3.45.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.45.0&targetVersion=GTv3.45.1&_a=commits) (2023-10-02)

### Bug Fixes

- **fix for drop down selection:** remove home country override on selection ([09e3ffb](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/09e3ffbe573ba53e2b477690c021f75f0b601cc9)) #5625270

#[3.45.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.44.3&targetVersion=GTv3.45.0&_a=commits) (2023-09-29)

### Features

- update auth sdk package ([26779a0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/26779a0749f0c78c5104fe26bc80b74ae1367110)) #5618620

##[3.44.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.44.2&targetVersion=GTv3.44.3&_a=commits) (2023-09-28)

### Bug Fixes

- sporadic loading indicator ([c5f3aa4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/c5f3aa4dd86b22079840ec9ea2089c2c5b9bf678)) #5612313

##[3.44.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.44.1&targetVersion=GTv3.44.2&_a=commits) (2023-09-28)

### Bug Fixes

- multiple verification emails being sent ([80fc7b6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/80fc7b6784e85cb16f38f7644f3602cc733abbbb)) #5612120

##[3.44.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.44.0&targetVersion=GTv3.44.1&_a=commits) (2023-09-27)

### Bug Fixes

- new translations %original_file_name% from Crowdin ([e0adea0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/e0adea0f7f6c47028af185a8db7b0301159dd80d))

#[3.44.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.43.1&targetVersion=GTv3.44.0&_a=commits) (2023-09-27)

### Features

- auto selecting home country ([83671b9](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/83671b9164d299aa90cd87fce2fd2c3fa05cb0f1)) #5268446

##[3.43.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.43.0&targetVersion=GTv3.43.1&_a=commits) (2023-09-27)

### Bug Fixes

- **updated customise screen:** wrong terms & conditions for NL ([23576b0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/23576b0e23eac82f01de6b6a1fce9cf1599ba6cd)) #5559409

#[3.43.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.42.0&targetVersion=GTv3.43.0&_a=commits) (2023-09-25)

### Features

- add spain to country options on customise screen ([f3ad93a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/f3ad93a1430f77c2b314159119529aef79b64034)) #5540097
  #5585096

#[3.42.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.41.0&targetVersion=GTv3.42.0&_a=commits) (2023-09-25)

### Features

- enable CTA buttons from add and update email screens ([454dc08](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/454dc08a29c8eac8c0c282304c56e057dea6dd25)) #5476979

#[3.41.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.40.0&targetVersion=GTv3.41.0&_a=commits) (2023-09-22)

### Features

- contributing update text for unable to save details modal ([8b0f9c9](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/8b0f9c95abdbde2cb072cb771ab502430504564c)) #5483227

#[3.40.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.39.0&targetVersion=GTv3.40.0&_a=commits) (2023-09-22)

### Features

- updating log in functionality in customise modal popup ([9d2081e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/9d2081e5f9213c6a78e55d6a2271ebbc84e83ec7)) #5480735

#[3.39.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.38.0&targetVersion=GTv3.39.0&_a=commits) (2023-09-20)

### Features

- updates to analytics ([58521a1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/58521a1ce90b8002fcb90f61efb3cbbddb4d95ad)) #5299461

#[3.38.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.37.0&targetVersion=GTv3.38.0&_a=commits) (2023-09-18)

### Features

- email verf update with auth sdk ([d1689e5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/d1689e5f13cc705df26981ff86b85e6209e005f5)) #5412257

#[3.37.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.36.0&targetVersion=GTv3.37.0&_a=commits) (2023-09-15)

### Features

- mock flow with auth sdk ([e16477a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/e16477a049169fe54944e352aa134f3be18813bb)) #5551873

#[3.36.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.35.0&targetVersion=GTv3.36.0&_a=commits) (2023-09-14)

### Features

- integrate auth sdk package ([154167f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/154167f60daf69738e2784166d4afe8b6041ffec)) #5532757

#[3.35.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.34.0&targetVersion=GTv3.35.0&_a=commits) (2023-09-11)

### Features

- contributing update email callback to the mfe ([b275557](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/b275557c0aac758de82de4b9ebbff72576280e29)) #5192154

#[3.34.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.33.0&targetVersion=GTv3.34.0&_a=commits) (2023-09-07)

### Features

- contributing new email address verified UI ([790fbf3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/790fbf3adbb4d1e3018f25c04ff51291d0e162f9)) #5300599

### Reverts

- reverting removal of pipeline security since it is now running correctly ([1511479](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/151147900e0b2c4ba16c326e3b5f2e01b227977d)) #5512997

#[3.33.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.32.0&targetVersion=GTv3.33.0&_a=commits) (2023-09-07)

### Features

- update to customise screen call to action request ([41efafe](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/41efafebc5fe4eac7122cd7c662988816d908a24)) #5292477

#[3.32.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.31.0&targetVersion=GTv3.32.0&_a=commits) (2023-09-07)

### Features

- updating screen navigation based on props ([6a6796c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/6a6796cd844bcb610006cf0b8c93bd21f2647452)) #5301531

#[3.31.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.30.0&targetVersion=GTv3.31.0&_a=commits) (2023-08-31)

### Features

- adding in the unable to save error modal to customise screen ([ef138ee](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/ef138eead1cc13143c5f2590d8b4c4391ee94912)) #5381872
  #5381872

#[3.30.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.29.0&targetVersion=GTv3.30.0&_a=commits) (2023-08-30)

### Features

- customise screen - disabling inputs when names provided ([f98f6fe](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/f98f6feea2fd7ba9d3f7454c514b8a6e81b27cf1)) #5382525

#[3.29.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.28.1&targetVersion=GTv3.29.0&_a=commits) (2023-08-30)

### Features

- added in the update email address screen ([95bcf0f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/95bcf0f1ee9c41686fc316589d1147eb7236c17e)) #5439238

##[3.28.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.28.0&targetVersion=GTv3.28.1&_a=commits) (2023-08-30)

### Bug Fixes

- temporary pipeline fix ([c0f6abf](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/c0f6abf85ec1381e3e6010c1769146ed04ac6591)) #5382525

#[3.28.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.27.0&targetVersion=GTv3.28.0&_a=commits) (2023-08-29)

### Features

- adding in error screen to verification ([03cebae](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/03cebae27ec6dcf74932c02d3fc10a0c6f375f19)) #5403664
  #5459942

#[3.27.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.26.0&targetVersion=GTv3.27.0&_a=commits) (2023-08-24)

### Features

- committing verification error modal screen ([a8d9e41](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/a8d9e418d2365c286d468d63f0dd459422b6c95c)) #5388414

#[3.26.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.25.0&targetVersion=GTv3.26.0&_a=commits) (2023-08-17)

### Features

- remove nl experience id from location settings ([dd59d97](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/dd59d973a1b63d7d037e71ec465e42b79812ace0))

#[3.25.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.24.0&targetVersion=GTv3.25.0&_a=commits) (2023-08-16)

### Features

- new setting up account screen ([095a2a6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/095a2a6c3b0b9fe2ac638dc32349d0cf4373934a)) #5272460

#[3.24.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.23.0&targetVersion=GTv3.24.0&_a=commits) (2023-08-14)

### Features

- add authenticate error screen ([e7b5b8d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/e7b5b8d2ae812199040c64ce5581a3d271cf7df6)) #5215068

#[3.23.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.22.1&targetVersion=GTv3.23.0&_a=commits) (2023-08-10)

### Features

- **add-email:** add new add-email-screen ([ce25c2e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/ce25c2e8fe3037114c9768537bf81ec4b0ee0c50)) #5237014
  #5299809

##[3.22.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.22.0&targetVersion=GTv3.22.1&_a=commits) (2023-08-09)

### Bug Fixes

- new translations %original_file_name% from Crowdin ([f876351](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/f876351c3c2103ddcae754c77f61dbea7b034772)) #5394163

#[3.22.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.21.0&targetVersion=GTv3.22.0&_a=commits) (2023-08-08)

### Features

- adding onCountryChange callback ([346df1a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/346df1a29993c1bb48e8267a932c5b36affc4d79)) #5230492

#[3.21.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.20.0&targetVersion=GTv3.21.0&_a=commits) (2023-08-03)

### Features

- added CIP exit modal ([217b727](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/217b7273b8e0c279139542cff9928bea6bd8a8e2)) #5337876

#[3.20.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.19.0&targetVersion=GTv3.20.0&_a=commits) (2023-08-03)

### Features

- customise screen (update) ([5628b5b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/5628b5b02b13a47d872649b74b704d6e05dbd8f4)) #5192156

#[3.19.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.18.2&targetVersion=GTv3.19.0&_a=commits) (2023-08-02)

### Features

- customise screen ([5e7f7be](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/5e7f7be2dcac13349945d665cf78de203cabc993)) #5192156

##[3.18.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.18.1&targetVersion=GTv3.18.2&_a=commits) (2023-07-27)

### Bug Fixes

- new translations %original_file_name% from Crowdin ([5a31f1a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/5a31f1a68224ebd261d6ac99fcb0803287bb59f9))

##[3.18.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.18.0&targetVersion=GTv3.18.1&_a=commits) (2023-07-27)

### Bug Fixes

- **screen:** add a check to stop double navigating to email verification screen ([fe1d8ce](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/fe1d8ced0b400facc133962782d879f1b45e5964)) #5303327

#[3.18.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.17.0&targetVersion=GTv3.18.0&_a=commits) (2023-07-19)

### Features

- **verify-email:** verify email against idp stored email as a workaround for missing idp property ([cc75dfc](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/cc75dfc49ce9c32119c521ffe44c1b7be9d1d891)) #5295025

#[3.17.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.16.1&targetVersion=GTv3.17.0&_a=commits) (2023-07-13)

### Features

- updating checkmarx pipeline ([42ccefa](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/42ccefa5aa055c42df81b68a5273367bcad25282)) #5272815

##[3.16.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.16.0&targetVersion=GTv3.16.1&_a=commits) (2023-07-11)

### Bug Fixes

- fix to longer than usual text not disappearing ([9b19d63](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/9b19d63cfd34566d43bdf8e2edb03eabaad74a09)) #5113697

#[3.16.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.15.1&targetVersion=GTv3.16.0&_a=commits) (2023-07-06)

### Features

- Account Onboarding ([fe8d770](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/fe8d770ffb682d5de36874447f0dc063d46b81aa))

##[3.15.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.15.0&targetVersion=GTv3.15.1&_a=commits) (2023-07-06)

### Bug Fixes

- set home location button is not visible due to height property ([5698992](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/5698992e6afc8b2e09ae9253fa176bfcb89d2540)) #5230506

#[3.15.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.14.0&targetVersion=GTv3.15.0&_a=commits) (2023-06-29)

### Features

- email verification for existing users ([16f963d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/16f963daa0db8a469b82cfc0085649cfc18be9b3)) #4959910

#[3.14.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.13.0&targetVersion=GTv3.14.0&_a=commits) (2023-06-26)

### Features

- add crowdin config file ([2008653](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/200865313428e23752790fc1811bd5e0f24cdbbd)) #5088189

#[3.13.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.12.0&targetVersion=GTv3.13.0&_a=commits) (2023-06-14)

### Features

- **edit email functionality:** edit email functionality ([1feb846](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/1feb846823abc0394588ae86ab52091ed86be297)) #4928056

#[3.12.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.11.1&targetVersion=GTv3.12.0&_a=commits) (2023-06-09)

### Features

- welcome back content changes ([f34929c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/f34929c201c3a091f42a3cc8113b1273badd452b)) #4928792

##[3.11.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.11.0&targetVersion=GTv3.11.1&_a=commits) (2023-06-09)

### Bug Fixes

- bug fix to keyboard not disappearing on input text when tapping outside ([33ab086](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/33ab0862a780f1f8e510a30f46846e47113bc27c)) #4707945

#[3.11.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.10.0&targetVersion=GTv3.11.0&_a=commits) (2023-06-02)

### Features

- added 2 error modals to the email verification screen ([199df76](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/199df76af7844a07d57167a1cbebc7f44e987199)) #4976844

#[3.10.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.9.0&targetVersion=GTv3.10.0&_a=commits) (2023-05-30)

### Features

- user logout on exit ([559bb9b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/559bb9baea00064c3ab804cf1b2a7a36cc7b10a7)) #4977963

#[3.9.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.8.1&targetVersion=GTv3.9.0&_a=commits) (2023-05-25)

### Features

- registration screen changes ([fe8a8de](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/fe8a8deac5742ca8d793e6ca0925ebba1733e4fd)) #4910607

##[3.8.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.8.0&targetVersion=GTv3.8.1&_a=commits) (2023-05-25)

### Bug Fixes

- changed UI of email verification screen ([d8a84bc](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/d8a84bc732d9f7c52ce3ce33d8734d39fd5d5762)) #4926814

#[3.8.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.7.4&targetVersion=GTv3.8.0&_a=commits) (2023-05-23)

### Features

- set home location screen uplift ([4e75252](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/4e75252298aee1c408e70ef0202c62919b67eece)) #4871500
  #4909920

##[3.7.4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.7.3&targetVersion=GTv3.7.4&_a=commits) (2023-05-23)

### Bug Fixes

- changing text to exit modal ([403a6ad](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/403a6ad60751b86aba69d50172f5eda6dd40f75a)) #4926984

- updating error message on sign up screen ([420622d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/420622d58899b9f01e235c30e6c5f75116f1ce36)) #4910637

##[3.7.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.7.2&targetVersion=GTv3.7.3&_a=commits) (2023-05-19)

### Bug Fixes

- sms verification ([e436d90](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/e436d9009e8f4d0f09dbb57e622aae849346d2e8)) #5005868

##[3.7.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.7.1&targetVersion=GTv3.7.2&_a=commits) (2023-05-09)

### Bug Fixes

- disable inputs while loading ([6c77871](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/6c778717279a167c2a29ffaeddedb998f3f55083)) #4750630

##[3.7.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.7.0&targetVersion=GTv3.7.1&_a=commits) (2023-04-18)

### Bug Fixes

- loading disabling buttons update ([53dad3e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/53dad3ebbbfcfc10cec5349e56d52461d1193b77)) #4750473

#[3.7.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.6.1&targetVersion=GTv3.7.0&_a=commits) (2023-04-13)

### Features

- **settings:** fix enum naming to closely match intended usage ([f1a9530](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/f1a953022e9950c5f5753f2aedc0706a6e7a051c)) #4750606

##[3.6.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.6.0&targetVersion=GTv3.6.1&_a=commits) (2023-04-07)

### Bug Fixes

- **secrets:** add additional url props to settings provider and support home... ([5e5ccb9](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/5e5ccb9b1e31b12fd18765c7f2d0e44d2be58806)) #4750606

#[3.6.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.5.0&targetVersion=GTv3.6.0&_a=commits) (2023-04-04)

### Features

- nl privacy policy env var update ([f139092](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/f13909267a7c3289fd1136f419cd5b3ce4df3603)) #4552142

#[3.5.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.4.0&targetVersion=GTv3.5.0&_a=commits) (2023-03-29)

### Features

- sonarqube coverage update ([20af22b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/20af22b4906bd6fcf1776fb57ff93865e9a6458f)) #4594957

#[3.4.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.3.0&targetVersion=GTv3.4.0&_a=commits) (2023-03-28)

### Features

- export analytics types ([d1e1d1f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/d1e1d1fe5fa51bf0dfd7d3980360562e3e64961a)) #4740128

#[3.3.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.2.0&targetVersion=GTv3.3.0&_a=commits) (2023-03-18)

### Features

- increased sonarqube coverage ([18242d6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/18242d6ade287d509aef510e265106a4675bc124)) #4594957

#[3.2.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.1.3&targetVersion=GTv3.2.0&_a=commits) (2023-03-16)

### Features

- update consent values and track marketing consent in user info context ([535aeaf](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/535aeaf4fa7f2cf73b4b7cc431dd36abd4e3c606)) #4620480

##[3.1.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.1.2&targetVersion=GTv3.1.3&_a=commits) (2023-03-16)

### Bug Fixes

- **solve sonarqube index error:** sonarqube fix ([a165f5c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/a165f5cde73b9a742ad92d06475d2d67bb46570b)) #4519960

##[3.1.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.1.1&targetVersion=GTv3.1.2&_a=commits) (2023-03-15)

### Bug Fixes

- add close icon to match provided designs ([3f11155](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/3f1115557c83eaec631f1fb7abdfc68f54e8d6e4)) #4473771

##[3.1.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.1.0&targetVersion=GTv3.1.1&_a=commits) (2023-03-14)

### Bug Fixes

- **sandbox:** remove sandbox alerts, replace with console info ([8903a95](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/8903a95a2c3637847921f293aadb53b4d2ae4207)) #4660046

#[3.1.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv3.0.0&targetVersion=GTv3.1.0&_a=commits) (2023-03-10)

### Features

- **add gopasswordless popup:** gopasswordless pop up ([6255a33](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/6255a334bd1030e05b4e6395c7ad66353870d39c)) #4446314
  #4446314

#[3.0.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv2.10.1&targetVersion=GTv3.0.0&_a=commits) (2023-03-10)

- Merged PR 260653: fix(./dist): update the path to the main index.js in package.json ([4e0218a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/4e0218ae42e81bbb09b0c0713b31fc241a470047)) #4202365

### BREAKING CHANGES

- This commit updates the way that the package is build and published. It fixes the
  error "Unable to find module "main" in package.json" When trying to consume this package"

##[2.10.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv2.10.0&targetVersion=GTv2.10.1&_a=commits) (2023-03-09)

### Bug Fixes

- update initial screen shown on start ([f3f487a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/f3f487ab74ba3f8930846d0b46b7091ca6660e16)) #4639455

#[2.10.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv2.9.0&targetVersion=GTv2.10.0&_a=commits) (2023-03-03)

### Features

- add analytics support ([1e4aa8e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/1e4aa8e1d276de0c28353f1286acb90f04aafc0b)) #4511265

#[2.9.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv2.8.1&targetVersion=GTv2.9.0&_a=commits) (2023-02-27)

### Features

- **registration be:** add in the gopasswordless apis ([246c1fe](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/246c1febf017fa5d35751194a79fd26e1582fbc7)) #4345126

##[2.8.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv2.8.0&targetVersion=GTv2.8.1&_a=commits) (2023-02-23)

### Bug Fixes

- fix to edit email error not showing in certain scenarios ([6b5ebce](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/6b5ebce9e5568aeac81d4d4ff9eca5e1c2c38632)) #4513330

#[2.8.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv2.7.1&targetVersion=GTv2.8.0&_a=commits) (2023-02-21)

### Features

- add login prop ([93123ba](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/93123badb8f86db865a6ed80aa414a15a3db3314)) #4504952
  #4511582

##[2.7.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv2.7.0&targetVersion=GTv2.7.1&_a=commits) (2023-02-20)

### Bug Fixes

- validate verification code ([0c9c80a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/0c9c80ab76558150302fcd422f5a91be19482d21)) #4525637

#[2.7.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv2.6.0&targetVersion=GTv2.7.0&_a=commits) (2023-02-15)

### Features

- disable SetHomeLocation screen for go live ([4087ead](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/4087eadd6270ace8a761159686d3a5ebd16a37b9)) #4478485

#[2.6.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv2.5.0&targetVersion=GTv2.6.0&_a=commits) (2023-02-10)

### Features

- email update errors to show alert pop up ([4d21074](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/4d21074dee4b2e51885febe3d6ed0235b7d41987)) #4250296

#[2.5.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv2.4.0&targetVersion=GTv2.5.0&_a=commits) (2023-02-10)

### Bug Fixes

- copy images to dist folder ([2254d66](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/2254d66fe6bf74f1a08e457a9476c449eedac4a5)) #4493722

### Features

- **welcome-back:** add welcome back page and modify routing for existing users ([5dc1f20](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/5dc1f203bde7dc3b8826d8228a800e6a00e7474d)) #4228559

#[2.4.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv2.3.0&targetVersion=GTv2.4.0&_a=commits) (2023-02-09)

### Features

- success screen styling ([d4c0457](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/d4c04578f908208f0bc467102fe52fe6948148df)) #4225243

#[2.3.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv2.2.0&targetVersion=GTv2.3.0&_a=commits) (2023-02-09)

### Features

- add success screen ([2850c7b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/2850c7be801c2a1ab66d1cabd66cbf1aca250913)) #4225243

#[2.2.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv2.1.0&targetVersion=GTv2.2.0&_a=commits) (2023-02-09)

### Features

- add error boundary and fallback screen ([4b152e8](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/4b152e8671f36bb7392ba485d5716a48946e5139)) #4482483

#[2.1.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv2.0.0&targetVersion=GTv2.1.0&_a=commits) (2023-02-08)

### Features

- add Lorenzo to secrets ([2bbd95b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/2bbd95babbb389f23d8368129206bea173013ff8))

#[2.0.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv1.1.0&targetVersion=GTv2.0.0&_a=commits) (2023-02-08)

- feat: goPasswordless Flow ([53a0758](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/53a0758e0dfa24eb0bbcca4ab462d26d18a64ecf))

### Features

- goPasswordless Flow ([71d428e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/71d428e1a8f73ca3e20950a379a2e50c990672f3)) #4225243

### BREAKING CHANGES

- Component requires a boolean attribute to decide which flow to take
- Component requires a boolean attribute to decide which flow to take

#[1.1.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/branchCompare?baseVersion=GTv1.0.0&targetVersion=GTv1.1.0&_a=commits) (2023-02-08)

### Features

- api and graphql queries for registration ([7c3e638](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/7c3e638ac2b7b871f67f6c3de0df6decc9c64b90)) #4093788

# 1.0.0 (2023-02-08)

### Bug Fixes

- fix unit tests ([3a55ad0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/3a55ad0e5f889911f377560e9c11cce4f0f98b11)) #4437156

- husky commit message prepare hook ([3f57150](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/3f57150f408f48b45b3b53fce478040bf4d615bf)) #4398448

- navigation fix ([bc57660](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/bc57660d9cedd395ea5de955e6c3b2cc9b1e9be7)) #4398448

- refactor: refactor navigation components ([1901695](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/1901695e2c41b330f0b3ca86e9c533d185c29d3c))

### Features

- add customizable alert component and display unable to save alert on re... ([a457aa6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/a457aa651a06a9b9a0e0f8f3d468b2f2e609fe21)) #4165327

- add is-ci check in prepare step ([6455c91](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/6455c91125370b3ecc5966a9c97086e09ba93fd8)) #4480791

- add unable to verify email alert ([dd730af](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/dd730affb6fcaeaeac6de7a47c9cf0005bf26c93)) #4203713

- add user registration details form ([944d635](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/944d635ccefad74591a5dbeea9879509ed71c329)) #4133752

- added Welcome Screen ([f4d876d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/f4d876db23a8fd0a512d26e5166e2f5206242ef4)) #4265724

- adding exit registration popup ([0c2a02b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/0c2a02bb45b8ace68ce93f5f7a191bb26adbb778)) #4203721

- **ci:** updated pipelines ([436c3a0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/436c3a0c652bd262dfb13daddbe0cfc437729b8c))

- **code:** adding editorconfig ([892217b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/892217bd13658ea9e14f5d185fbd41dfe5f55250))

- **email-verification:** add email verification and edit email verification... ([e25cf2e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/e25cf2ef2b69504635b147b4171dc7274f9922e6)) #4096499

- enable release pipeline using sdk shared config ([347a771](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/347a771f005fbf8d9176286b4f4f71e36d20b106)) #3992382

- initial commit of sms verification ([fdcb98c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/fdcb98c135822ffcfdda117a363a00f505f8a694)) #4093788

- **lint:** #3476819 implemented linting standards ([e283ba2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/e283ba23a65138f2826f3fa0480cb5ba1d676031))

- **register-screen:** add initial gql verification on register screen and up... ([e467fe6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/e467fe6d2b51768fd03b45c6a419885c035c2bb1)) #4228559

- semantic release ([e10afbd](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/e10afbd5669a5320b5036bc0a688f95ffe2c38d6)) #4480791

- sms limit exceeded ([6c977c7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/6c977c7b8fdc1ec09953a413f19cef941ecba93c)) #4214983

- update policy urls based on home country selected and add urls to secrets ([be66d2f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-registration/commit/be66d2f5aad7a50adf7046109503f5959291f977)) #4202920

### BREAKING CHANGES

- removed NavigationContainer from screens as it will result in nested
